<?php

namespace Modules\Ai;

use <PERSON><PERSON><PERSON>\ModuleServiceProvider;
use Modules\User\Helpers\PermissionHelper;
use Modules\Ai\Drivers\AiDriver;
use Modules\Ai\Drivers\OpenAi;
use Modules\Ai\Models\Ai;
use Illuminate\Support\Facades\Config;

class ModuleProvider extends ModuleServiceProvider
{

    public function boot()
    {
        if (is_installed() and Ai::isEnable()) {
            $this->loadViewsFrom(__DIR__ . '/Views', 'Ai');

            $this->mergeConfigFrom(__DIR__ . '/Configs/config.php', 'ai');

            // Load AI settings from database into config
            $this->loadAiConfigFromDB();

            $this->loadRoutesFrom(__DIR__ . '/Routes/web.php');

            add_action('ADMIN_JS_STACK', [$this, '__addJs']);

            $this->app->singleton(AiDriver::class, function () {
                $settings = config('ai.providers');
                $default = config('ai.default');
                if (empty($settings[$default])) {
                    throw new \Exception("AI Driver not found");
                }
                return new OpenAi($settings[$default]);
            });
        }

        PermissionHelper::add([
            "ai_text_generate"
        ]);
    }

    public function register()
    {
        $this->app->register(RouterServiceProvider::class);
        parent::register();
    }

    public function __addJs()
    {
        echo view("Ai::frontend.text-generate");
    }

    public static function getAdminMenu()
    {
        if (!Ai::isEnable()) return [];

        return [
            'ai' => [
                'title' => __("AI Settings"),
                'url' => route('core.admin.settings.index', ['group' => 'ai']),
                "permission" => "setting_update",
                "position" => 80,
                'icon' => "fa fa-robot",
                "group" => "system",
            ]
        ];
    }

    public static function getName()
    {
        return 'AI';
    }

    public static function getDesc()
    {
        return __('AI text generation and tools');
    }

    public static function getVersion()
    {
        return '1.0';
    }

    public static function getAuthor()
    {
        return 'MAZAR Travel';
    }

    /**
     * Load AI configuration from database settings
     */
    protected function loadAiConfigFromDB()
    {
        // Load AI API key from database
        if ($apiKey = setting_item('ai_api_key')) {
            Config::set('ai.providers.main.api_key', $apiKey);
        }

        // Load AI model name from database
        if ($modelName = setting_item('ai_model_name')) {
            Config::set('ai.providers.main.model', $modelName);
        }
    }
}
